"""
Strings and localization management for Lionaire platform.
This file contains all user-facing text strings for easy translation and customization.
"""

from typing import Dict, Any


class Strings:
    """
    Central string management for Lionaire platform.
    All user-facing text should be defined here for easy localization.
    """
    
    # Project metadata
    PROJECT = {
        "name": "Lionaire",
        "full_name": "Lionaire Platform",
        "description": "Advanced Trading Analysis & Backtesting Platform",
        "tagline": "Professional Forex Trading Analysis Platform",
        "version": "0.2.0",
        "phase": "Phase 2: Visualization & Charts",
        "author": "Lionaire Development Team"
    }
    
    # Main application
    APP = {
        "title": "📈 Lionaire Platform",
        "subtitle": "Advanced Trading Analysis & Backtesting Platform",
        "footer": "Lionaire Platform v0.2.0 - Phase 2: Visualization & Charts"
    }
    
    # Header metrics
    HEADER = {
        "available_pairs": "Available Pairs",
        "timeframes": "Timeframes",
        "indicators": "Indicators",
        "memory_usage": "Memory Usage"
    }
    
    # Data selection
    DATA_SELECTION = {
        "title": "📊 Data Selection",
        "currency_pair": "Currency Pair",
        "currency_pair_help": "Select the currency pair to analyze",
        "timeframe": "Timeframe",
        "timeframe_help": "Select the chart timeframe",
        "date_range": "📅 Date Range",
        "date_range_option": "Date Range Option",
        "last_n_candles": "Last N Candles",
        "date_range_custom": "Date Range",
        "all_available": "All Available",
        "number_of_candles": "Number of Candles",
        "number_of_candles_help": "Number of most recent candles to load",
        "start_date": "Start Date",
        "end_date": "End Date",
        "load_data": "📈 Load Data",
        "load_data_help": "Load selected data and create chart",
        "quick_timeframe_switch": "⚡ Quick Timeframe Switch"
    }
    
    # Chart component
    CHART = {
        "title": "📈 Price Chart",
        "refresh_chart": "🔄 Refresh Chart",
        "refresh_chart_help": "Reload data and refresh chart",
        "auto_scale": "🎯 Auto Scale",
        "auto_scale_help": "Auto-scale chart to fit data",
        "export_data": "💾 Export Data",
        "export_data_help": "Export current chart data to CSV",
        "fullscreen": "🔍 Fullscreen",
        "fullscreen_help": "Open chart in fullscreen mode",
        "advanced_settings": "⚙️ Advanced Settings",
        "display_options": "Display Options",
        "show_volume": "Show Volume",
        "show_volume_help": "Display volume bars below price chart",
        "show_grid": "Show Grid",
        "show_grid_help": "Display grid lines on chart",
        "crosshair_options": "Crosshair Options",
        "show_vertical_line": "Show Vertical Line",
        "show_vertical_line_help": "Display vertical crosshair line",
        "show_horizontal_line": "Show Horizontal Line",
        "show_horizontal_line_help": "Display horizontal crosshair line with price level",
        "remove_gaps": "Remove Weekend Gaps",
        "remove_gaps_help": "Remove gaps in chart during weekends/holidays for continuous display",
        "price_display": "Price Display",
        "price_precision": "Price Precision",
        "price_precision_help": "Number of decimal places for price display",
        "candlestick_style": "Candlestick Style",
        "candlestick_style_help": "Chart display style",
        "theme": "Theme",
        "theme_help": "Chart color theme",
        "warm_up_candles": "Warm-up Candles",
        "warm_up_candles_help": "Number of candles to use for indicator warm-up (prevents look-ahead bias)"
    }
    
    # Chart styles and themes
    CHART_STYLES = {
        "candlestick": "Candlestick",
        "ohlc_bars": "OHLC Bars",
        "line": "Line"
    }
    
    CHART_THEMES = {
        "plotly": "Plotly",
        "plotly_white": "Plotly White",
        "plotly_dark": "Plotly Dark",
        "ggplot2": "GGPlot2",
        "seaborn": "Seaborn",
        "simple_white": "Simple White"
    }
    
    # Technical indicators
    INDICATORS = {
        "title": "📊 Technical Indicators",
        "add_indicators": "➕ Add Indicators",
        "manage_indicators": "⚙️ Manage Indicators",
        "presets": "💾 Presets",
        "category": "Category",
        "indicator_type": "Indicator Type",
        "add_indicator": "Add Indicator",
        "summary": "📋 Indicator Summary",
        "total": "Total",
        "enabled": "Enabled",
        "disabled": "Disabled",
        "categories": "Categories",
        "by_category": "By Category:",
        "remove": "Remove",
        "toggle": "Toggle",
        "configure": "Configure",
        "color": "Color",
        "line_width": "Line Width",
        "opacity": "Opacity"
    }
    
    # Indicator categories
    INDICATOR_CATEGORIES = {
        "trend": "Trend",
        "momentum": "Momentum", 
        "volatility": "Volatility",
        "volume": "Volume",
        "support_resistance": "Support/Resistance",
        "oscillators": "Oscillators"
    }
    
    # Memory management
    MEMORY = {
        "title": "💾 Memory Usage",
        "cache_entries": "Cache Entries",
        "cache_size": "Cache Size (MB)",
        "cache_usage": "Cache Usage %",
        "clear_cache": "🗑️ Clear Cache",
        "cache_cleared": "Cache cleared!"
    }
    
    # Messages
    MESSAGES = {
        "loading": "Loading...",
        "refreshing_chart": "Refreshing chart...",
        "switching_timeframe": "Switching to {timeframe}...",
        "chart_refreshed": "Chart refreshed!",
        "data_loaded": "Data loaded successfully!",
        "no_pairs_available": "No currency pairs available. Please check your data directory.",
        "please_load_data": "Please load data first using the Data Selection panel.",
        "please_load_data_indicators": "Please load data first to use indicators.",
        "failed_refresh_chart": "Failed to refresh chart",
        "application_error": "Application error: {error}",
        "error_details": "Error Details",
        "refresh_page": "Please refresh the page or check the logs for more information.",
        "data_export_success": "Data exported successfully to {filename}",
        "data_export_error": "Error exporting data: {error}"
    }
    
    # Buttons and actions
    BUTTONS = {
        "load": "Load",
        "refresh": "Refresh", 
        "export": "Export",
        "clear": "Clear",
        "add": "Add",
        "remove": "Remove",
        "configure": "Configure",
        "save": "Save",
        "cancel": "Cancel",
        "apply": "Apply",
        "reset": "Reset"
    }
    
    # File operations
    FILES = {
        "export_filename": "{pair}_{timeframe}_data_{date}.csv",
        "chart_filename": "{pair}_{timeframe}_chart"
    }


# Global strings instance
strings = Strings()


def get_string(category: str, key: str, **kwargs) -> str:
    """
    Get a localized string with optional formatting.
    
    Args:
        category: String category (e.g., 'APP', 'CHART', 'MESSAGES')
        key: String key within the category
        **kwargs: Format arguments for string formatting
        
    Returns:
        Formatted string or key if not found
    """
    try:
        category_dict = getattr(strings, category.upper())
        text = category_dict.get(key, key)
        
        if kwargs:
            return text.format(**kwargs)
        return text
    except (AttributeError, KeyError):
        return key


def get_project_info() -> Dict[str, Any]:
    """Get project information."""
    return strings.PROJECT.copy()


def get_app_config() -> Dict[str, Any]:
    """Get application configuration strings."""
    return {
        "page_title": strings.PROJECT["full_name"],
        "page_icon": "📈",
        "layout": "wide"
    }
